// Global audio manager for persistent background music across pages using iframe
class AudioManager {
  constructor() {
    this.iframe = null;
    this.isPlaying = false;
    this.currentTime = 0;
    this.volume = 0.3; // Default volume (30%)
    this.duration = 0;
    this.storageKey = 'proposalWebsiteAudio';
    this.ready = false;

    // Initialize iframe-based audio player
    this.initializeIframe();
    this.setupMessageListener();
  }

  initializeIframe() {
    // Check if iframe already exists (for page navigation)
    this.iframe = document.getElementById('persistent-audio-player');

    if (!this.iframe) {
      this.iframe = document.createElement('iframe');
      this.iframe.id = 'persistent-audio-player';
      this.iframe.src = 'audio-player.html';
      this.iframe.style.cssText = `
        position: fixed;
        top: -1000px;
        left: -1000px;
        width: 1px;
        height: 1px;
        border: none;
        opacity: 0;
        pointer-events: none;
        z-index: -1000;
      `;
      document.body.appendChild(this.iframe);
    }
  }

  setupMessageListener() {
    window.addEventListener('message', (event) => {
      if (event.data.source !== 'audio-player') return;

      const { type, data } = event.data;

      switch (type) {
        case 'play':
          this.isPlaying = true;
          this.updateControls();
          break;
        case 'pause':
          this.isPlaying = false;
          this.updateControls();
          break;
        case 'timeupdate':
          this.currentTime = data.currentTime;
          this.duration = data.duration;
          break;
        case 'playSuccess':
          this.isPlaying = true;
          this.updateControls();
          break;
        case 'playFailed':
          console.warn('Audio play failed:', data.error);
          this.showPlayButton();
          break;
        case 'volumeChanged':
          this.volume = data.volume;
          break;
        case 'state':
          this.isPlaying = data.isPlaying;
          this.currentTime = data.currentTime;
          this.duration = data.duration;
          this.volume = data.volume;
          this.ready = true;
          this.updateControls();
          break;
        case 'error':
          console.warn('Audio error:', data.error);
          break;
      }
    });
  }

  sendMessage(action, data = {}) {
    if (this.iframe && this.iframe.contentWindow) {
      this.iframe.contentWindow.postMessage({ action, data }, '*');
    }
  }

  play() {
    this.sendMessage('play');
  }

  pause() {
    this.sendMessage('pause');
  }

  toggle() {
    this.sendMessage('toggle');
  }

  setVolume(vol) {
    this.volume = Math.max(0, Math.min(1, vol));
    this.sendMessage('setVolume', { volume: this.volume });
  }

  getState() {
    this.sendMessage('getState');
  }

  updateControls() {
    const toggleBtn = document.querySelector('.audio-toggle .audio-icon');
    if (toggleBtn) {
      toggleBtn.textContent = this.isPlaying ? '⏸️' : '▶️';
    }
  }

  // Initialize after iframe loads
  init() {
    // Wait for iframe to load, then get initial state
    setTimeout(() => {
      this.getState();
      // Try to start playing after a delay
      setTimeout(() => {
        this.play();
      }, 1000);
    }, 500);
  }

  // Show a play button for user interaction (required for autoplay policies)
  showPlayButton() {
    if (document.querySelector('.audio-play-prompt')) return; // Already shown
    
    const playPrompt = document.createElement('div');
    playPrompt.className = 'audio-play-prompt';
    playPrompt.innerHTML = `
      <div class="audio-prompt-content">
        <div class="audio-prompt-icon">🎵</div>
        <p>Click to play background music</p>
        <button class="audio-prompt-btn">Play Music</button>
      </div>
    `;
    
    // Add styles
    playPrompt.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(214, 51, 108, 0.95);
      color: white;
      padding: 15px 20px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 1000;
      font-family: inherit;
      backdrop-filter: blur(10px);
      animation: slideInRight 0.3s ease-out;
    `;
    
    const btn = playPrompt.querySelector('.audio-prompt-btn');
    btn.style.cssText = `
      background: white;
      color: var(--primary, #d6336c);
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 600;
      margin-top: 8px;
      transition: transform 0.2s ease;
    `;
    
    btn.addEventListener('click', () => {
      this.play();
      playPrompt.remove();
    });
    
    btn.addEventListener('mouseenter', () => {
      btn.style.transform = 'scale(1.05)';
    });
    
    btn.addEventListener('mouseleave', () => {
      btn.style.transform = 'scale(1)';
    });
    
    document.body.appendChild(playPrompt);
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
      if (playPrompt.parentNode) {
        playPrompt.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => playPrompt.remove(), 300);
      }
    }, 10000);
  }

  // Create audio controls UI
  createControls() {
    if (document.querySelector('.audio-controls')) return; // Already exists

    const controls = document.createElement('div');
    controls.className = 'audio-controls';
    controls.innerHTML = `
      <button class="audio-toggle" title="Toggle music">
        <span class="audio-icon">${this.isPlaying ? '⏸️' : '▶️'}</span>
      </button>
    `;
    
    // Add styles
    controls.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.95);
      padding: 10px;
      border-radius: 50%;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      transition: opacity 0.3s ease;
    `;
    
    const toggleBtn = controls.querySelector('.audio-toggle');
    toggleBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 5px;
      border-radius: 50%;
      transition: background 0.2s ease;
    `;
    
    // Event listeners
    toggleBtn.addEventListener('click', () => {
      this.toggle();
      toggleBtn.querySelector('.audio-icon').textContent = this.isPlaying ? '⏸️' : '▶️';
    });
    
    toggleBtn.addEventListener('mouseenter', () => {
      toggleBtn.style.background = 'rgba(214, 51, 108, 0.1)';
    });
    
    toggleBtn.addEventListener('mouseleave', () => {
      toggleBtn.style.background = 'none';
    });
    
    document.body.appendChild(controls);
    
    // Auto-hide controls after inactivity
    let hideTimeout;
    const resetHideTimeout = () => {
      clearTimeout(hideTimeout);
      controls.style.opacity = '1';
      hideTimeout = setTimeout(() => {
        controls.style.opacity = '0.7';
      }, 3000);
    };
    
    controls.addEventListener('mouseenter', () => {
      clearTimeout(hideTimeout);
      controls.style.opacity = '1';
    });
    
    controls.addEventListener('mouseleave', resetHideTimeout);
    
    resetHideTimeout();
  }
}

// Create global instance
window.audioManager = window.audioManager || new AudioManager();

// Export for module usage
export default window.audioManager;
